<!-- MessageItem.svelte (Safer Version) -->
<script lang="ts">
	import { t, language } from '$lib/stores/i18n';
	import type { Message } from '$lib/types/customer';
	import { formatMessageTime } from '$lib/utils/messageFormatter';
	import { getInitials } from '$lib/utils/avatarGenerator';
	import LineMessageRenderer from '$lib/components/renderer/LineMessageRenderer.svelte';
	import ImageModal from '$lib/components/renderer/ImageModal.svelte';

	export let message: Message;
	export let showAvatar: boolean = true;
	
	// Image modal state
	let isModalOpen = false;
	let modalImageUrl = '';
	let modalImageAlt = '';
	
	function getAvatarColor(name: string) {
		const colors = [
			'bg-blue-500',
			'bg-green-500',
			'bg-yellow-500',
			'bg-red-500',
			'bg-purple-500',
			'bg-pink-500',
			'bg-indigo-500',
		];
		
		let hash = 0;
		for (let i = 0; i < name.length; i++) {
			hash = name.charCodeAt(i) + ((hash << 5) - hash);
		}
		
		return colors[Math.abs(hash) % colors.length];
	}
	
	function openImageModal(imageUrl: string, alt: string = 'Image') {
		modalImageUrl = imageUrl;
		modalImageAlt = alt;
		isModalOpen = true;
		
		// Prevent body scroll
		document.body.classList.add('modal-open');
	}
	
	function closeImageModal() {
		isModalOpen = false;
		modalImageUrl = '';
		modalImageAlt = '';
		
		// Restore body scroll
		document.body.classList.remove('modal-open');
	}

	function handleAction(actionData) {
		console.log('Action received in MessageItem:', actionData);
		switch(actionData.platform) {
			case 'line':
				break;
			case 'facebook':
				break;
			case 'whatsapp':
				break;
		}
	}
</script>

<div class="mb-4 flex {message.is_self ? 'justify-end' : 'justify-start'}">
	<div class="flex {message.is_self ? 'flex-row-reverse' : 'flex-row'} items-end max-w-xs lg:max-w-md">
		<!-- Avatar -->
		<!-- {#if !message.is_self && showAvatar}
			<div class="flex-shrink-0 mx-2">
				<div class="{getAvatarColor(message.user_name)} w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium">
					{getInitials(message.user_name)}
				</div>
			</div>
		{:else if !message.is_self}
			<div class="w-8 mx-2"></div>
		{/if} -->
		
		<!-- Message bubble -->
		<div class="flex flex-col {message.is_self ? 'items-end' : 'items-start'}">
			{#if showAvatar} 
				<span class="text-xs text-gray-500 mb-1 px-1">{message.user_name}</span>
			{/if}
			
			{#if message.message_type === 'ALTERNATIVE'}
				<!-- Check if message_template has content before rendering LineMessageRenderer -->
				{#if message.message_template && Object.keys(message.message_template).length > 0}
					<!-- Render LineMessageRenderer for ALTERNATIVE messages with valid template -->
					<div class="rounded-lg shadow-sm">
						<LineMessageRenderer messageData={message.message_template} onAction={handleAction} />
					</div>
				{:else}
					<!-- Fallback to display "text" when message_template is empty or invalid -->
					<div class="{message.is_self ? 'bg-blue-500 text-white' : 'bg-white border border-gray-200'} rounded-lg px-4 py-2 shadow-sm">
						<div class="text-sm break-words" style="word-break: break-word;">
							{t('message_template_empty_or_invalid')}
						</div>
					</div>
				{/if}
			{:else if message.message_type === 'IMAGE'}
				<!-- Image message with clickable images -->
				<div class="{message.is_self ? 'bg-blue-500 text-white' : 'bg-white border border-gray-200'} rounded-lg px-4 py-2 shadow-sm">
					<div class="text-sm break-words" style="word-break: break-word;">
						{#if Array.isArray(message.file_url)}
							{#each message.file_url as url, idx}
								<img 
									src={url} 
									alt="Image {idx + 1}" 
									class="max-w-full h-auto cursor-pointer hover:opacity-80 transition-opacity rounded {idx < message.file_url.length - 1 ? 'mb-2' : ''}"
									on:click={() => openImageModal(url, `Image ${idx + 1}`)}
									on:keydown={(e) => e.key === 'Enter' && openImageModal(url, `Image ${idx + 1}`)}
									role="button"
									tabindex="0"
								/>
							{/each}
						{:else if message.file_url}
							<img 
								src={message.file_url} 
								alt="Image" 
								class="max-w-full h-auto cursor-pointer hover:opacity-80 transition-opacity rounded"
								on:click={() => openImageModal(message.file_url, 'Image')}
								on:keydown={(e) => e.key === 'Enter' && openImageModal(message.file_url, 'Image')}
								role="button"
								tabindex="0"
							/>
						{:else}
							<div class="text-blue-600">📷 Image message</div>
						{/if}
					</div>
				</div>
			{:else}
				<!-- Updated section for FILE message type handling -->
				<div class="{message.is_self ? 'bg-blue-500 text-white' : 'bg-white border border-gray-200'} rounded-lg px-4 py-2 shadow-sm">
					<div class="text-sm break-words" style="word-break: break-word;">
						{#if message.message_type === 'FILE'}
							{#if message.file_url}
								{@const fileName = message.file_name || 'file'}
								{@const fileExtension = fileName.split('.').pop()?.toLowerCase() || ''}
								{@const isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp'].includes(fileExtension)}
								
								{#if isImage}
									<!-- Display image if file is an image -->
									{#if Array.isArray(message.file_url)}
										{#each message.file_url as url, idx}
											<img 
												src={url} 
												alt="Image {idx + 1}" 
												class="max-w-full h-auto cursor-pointer hover:opacity-80 transition-opacity rounded {idx < message.file_url.length - 1 ? 'mb-2' : ''}"
												on:click={() => openImageModal(url, `Image ${idx + 1}`)}
												on:keydown={(e) => e.key === 'Enter' && openImageModal(url, `Image ${idx + 1}`)}
												role="button"
												tabindex="0"
											/>
										{/each}
									{:else}
										<img 
											src={message.file_url} 
											alt={fileName} 
											class="max-w-full h-auto cursor-pointer hover:opacity-80 transition-opacity rounded"
											on:click={() => openImageModal(message.file_url, fileName)}
											on:keydown={(e) => e.key === 'Enter' && openImageModal(message.file_url, fileName)}
											role="button"
											tabindex="0"
										/>
									{/if}
								{:else}
									<!-- Display document icon for non-image files -->
									<div class="flex items-center space-x-2 cursor-pointer hover:opacity-80 transition-opacity" 
										on:click={() => window.open(message.file_url, '_blank')}
										on:keydown={(e) => e.key === 'Enter' && window.open(message.file_url, '_blank')}
										role="button"
										tabindex="0">
										<svg class="w-6 h-6 {message.is_self ? 'text-white' : 'text-blue-600'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
										</svg>
										<div class="flex flex-col">
											<span class="font-medium">{fileName}</span>
											<span class="text-xs {message.is_self ? 'text-blue-100' : 'text-gray-500'}">Click to download</span>
										</div>
									</div>
								{/if}
							{:else}
								<div class="{message.is_self ? 'text-white' : 'text-blue-600'}">📎 File attachment</div>
							{/if}
						{:else}
							{@html message.message.replace(/\n/g, '<br>')}
						{/if}
					</div>
				</div>
			{/if}
			
			<div class="flex items-center mt-1 px-1 space-x-2">
				<span class="text-xs text-gray-400">
					{formatMessageTime(message.created_on)}
				</span>
				
				{#if message.is_self}
					<!-- Message status indicators -->
					{#if message.status === 'SENT'}
						<svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
						</svg>
					{:else if message.status === 'DELIVERED'}
						<svg class="w-3 h-3 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
							<path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" />
						</svg>
					{:else if message.status === 'read'}
						<svg class="w-3 h-3 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
							<path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" />
						</svg>
					{/if}
				{/if}
			</div>
		</div>
	</div>
</div>

<!-- Image Modal -->
<ImageModal 
	bind:isOpen={isModalOpen}
	imageUrl={modalImageUrl}
	imageAlt={modalImageAlt}
	on:close={closeImageModal}
/>

<style>
	:global(.modal-open) {
		overflow: hidden;
	}
</style>