<script lang="ts">
    import ChatBox from '$lib/components/chat/ChatBox.svelte';
    import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';
    import { Heading, Button, Dropdown, DropdownItem } from 'flowbite-svelte';
    import { DotsVerticalOutline, UserSolid } from 'flowbite-svelte-icons';
    import { slide } from 'svelte/transition';
    import { onMount } from 'svelte';
    import type { PageData } from './$types';
    import TicketInfoPanel from './InfoDisplayPanel/TicketInfoPanel.svelte';

    export let data: PageData;
    $: ({
      ticketId,
      ticket,
      ticket_messages,
      customer_policyholders,
      users,
      statuses,
      priorities,
      customer_notes,
      ticket_summaries,
      ticket_topics,
      ticket_owners,
      loginUser
      // error
    } = data);
    let access_token = data.access_token;
    $: customerName = ticket?.customer.name;
    
    // Splitter control
    let rightPanelWidth = 380; // Default width in pixels
    let isDragging = false;
    let minPanelWidth = 280;
    let maxPanelWidth = 600;
    let currentLayout = 'default'; // 'default', '25-75', '75-25'
    
    // Track window width for responsive design
    let windowWidth = 0;
    onMount(() => {
      windowWidth = window.innerWidth;
      const handleResize = () => {
        windowWidth = window.innerWidth;
        // Adjust panel width if it exceeds window width
        if (rightPanelWidth > windowWidth * 0.6) {
          rightPanelWidth = Math.max(minPanelWidth, windowWidth * 0.4);
        }
      };
      window.addEventListener('resize', handleResize);
      return () => {
        window.removeEventListener('resize', handleResize);
      };
    });
    
    // Layout presets
    function setLayout(layout: string) {
      currentLayout = layout;
      switch (layout) {
        case '25-75':
          rightPanelWidth = windowWidth * 0.75;
          break;
        case '75-25':
          rightPanelWidth = windowWidth * 0.25;
          break;
        case 'default':
        default:
          rightPanelWidth = 380;
          break;
      }
      // Ensure within bounds
      rightPanelWidth = Math.max(minPanelWidth, Math.min(windowWidth * 0.8, rightPanelWidth));
    }
    
    // Toggle between layouts
    function toggleLayout() {
      switch (currentLayout) {
        case 'default':
          setLayout('25-75');
          break;
        case '25-75':
          setLayout('75-25');
          break;
        case '75-25':
        default:
          setLayout('default');
          break;
      }
    }
    
    // Splitter drag functionality
    function handleMouseDown(event: MouseEvent) {
      isDragging = true;
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      event.preventDefault();
    }
    
    function handleMouseMove(event: MouseEvent) {
      if (!isDragging) return;
      
      const newWidth = windowWidth - event.clientX;
      rightPanelWidth = Math.max(minPanelWidth, Math.min(windowWidth * 0.8, newWidth));
      
      // Update current layout based on approximate ratios
      const ratio = rightPanelWidth / windowWidth;
      if (ratio > 0.65) {
        currentLayout = '25-75';
      } else if (ratio < 0.35) {
        currentLayout = '75-25';
      } else {
        currentLayout = 'default';
      }
    }
    
    function handleMouseUp() {
      isDragging = false;
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    }
</script>
    
<svelte:head>
      <title>Ticket Details</title>
</svelte:head>
    
<div>      
    <!-- Main content area with splitter -->
    <div class="flex h-auto sm:h-[calc(100vh-64px)] md:h-[calc(100vh-64px)] ">
         <!-- Chat area - adjusts width based on right panel -->
         <div 
            class="flex-grow flex flex-col min-h-0 transition-all duration-150"
            style="width: calc(100% - {rightPanelWidth}px);">
             <ChatBox
                {ticket}
                {ticketId}
                {access_token}
                {users}
                {priorities}
                {statuses}
                {loginUser}
                {ticket_topics}
                status_id={ticket?.status_id}
                ownerUsername={ticket.owner.username}
                loginUsername={loginUser.username}
                height="100%"
             />
         </div>
        
         <!-- Right info panel -->
         <div 
             class="bg-white flex-shrink-0 transition-all duration-150"
             style="width: {rightPanelWidth}px;">
            <TicketInfoPanel {ticketId} {ticket} {ticket_owners} {ticket_summaries} {customer_notes} />
         </div>
     </div>
</div>
    
<style>
    .scrollbar-stable {
      scrollbar-gutter: stable;
    }
    
    /* Style the scrollbar */
    ::-webkit-scrollbar {
      width: 8px;
    }
    
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
    }
    
    ::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 10px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: #a1a1a1;
    }
    
    /* Prevent text selection while dragging */
    body.dragging {
      user-select: none;
    }
</style>