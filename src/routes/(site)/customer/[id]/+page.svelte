<script lang="ts">
    import { t } from '$lib/stores/i18n';

	import {
		Breadcrumb,
		BreadcrumbItem,
        Tabs, 
        TabItem,
        Button,
	} from 'flowbite-svelte';

    import { getBackendUrl } from '$src/lib/config';
	import type { PageData } from './$types';
    import { enhance } from '$app/forms';
	import { getColorClass } from '$lib/utils';
	import type { CustomerInterface } from '$src/lib/api/types/customer';

    import Memory from '$src/lib/components/customer/detail/memory.svelte';
    import Notes from '$src/lib/components/customer/detail/notes.svelte';
    import Policy from '$src/lib/components/customer/detail/policy.svelte';
    import Ticket from '$src/lib/components/customer/detail/ticket.svelte';
    import Profile from '$src/lib/components/customer/detail/profile.svelte';

	export let data: PageData;

	$: ({ customer, customer_notes, customer_policies, customer_tickets, customer_tags, customer_memory, access_token} = data);

    function exportCustomerChat() {
        const url = `${getBackendUrl()}/customer/api/customers/${customer.customer_id}/messages/`;
        console.log(url);
        const bodyData = { "format": "zip" };

        fetch(url, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${access_token}`,
				'Content-Type': 'application/json' // Ensure JSON request
			},
			body: JSON.stringify(bodyData)
		})
			.then((response) => response.blob())
			.then((blob) => {
				const link = document.createElement('a');
				link.href = URL.createObjectURL(blob);
				link.click();
			})
			.catch((error) => {
				console.error('Error downloading the file:', error);
			});
    }
</script>

<svelte:head>
	<title>Customer Details</title>
</svelte:head>

<div class="min-h-screen bg-white rounded-lg">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
        <!--Breadcrumb -->
        <div>
            <Breadcrumb aria-label="Default breadcrumb example">
                <BreadcrumbItem href="/" home>
                    <span class="text-gray-400">{t('home')}</span>
                </BreadcrumbItem>
                <BreadcrumbItem href="/customer">
                    <span class="text-gray-400">{t('customers')}</span>
                </BreadcrumbItem>
                <BreadcrumbItem>
                    <span class="text-gray-700">{t('detail')}</span>
                </BreadcrumbItem>
            </Breadcrumb>
        </div>

        <!--Customer Informations and Notes -->
        <div class="grid grid-cols-1 lg:grid-cols-10 gap-5 mt-4"> 
            <!-- Right side: Policy and Ticket Info -->
            <div class="lg:col-span-7 border rounded-lg bg-white shadow-md p-4">
                <Tabs tabStyle="underline">
                    <TabItem open title="{t('tickets')}">
                        <Ticket {customer_tickets}/>
                    </TabItem>

                    <TabItem title="{t('policies')}">
                        <Policy {customer_policies} />

                    </TabItem>

                    <TabItem title="{t('notes')}">
                        <Notes {access_token} {customer} {customer_notes}/>
                    </TabItem>

                    <TabItem title="{t('memories')}">
                        <Memory memories={customer_memory} />
                    </TabItem>
                </Tabs>
            </div>

            <!-- Left side: Customer Profile and Notes -->
            <div class="lg:col-span-3 space-y-4">
                <Profile {customer} {customer_tags}/>

                <div class="flex flex-col space-y-3">
                    <Button class="text-gray-700 bg-white border hover:bg-gray-100 flex items-center gap-2 shadow-md" on:click={exportCustomerChat}>
                        {t('export_customer_conversations')}
                    </Button>
                </div>
            </div>
        </div>
    </div>
</div>
